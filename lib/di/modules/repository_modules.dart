import 'package:dauntless/di/modules/network_modules.dart';
import 'package:dauntless/repositories/implementations/file_match_repository.dart';
import 'package:dauntless/repositories/implementations/local_match_repository.dart';
import 'package:dauntless/repositories/implementations/network_match_repository.dart';
import 'package:dauntless/repositories/interfaces/match_persistence_repository_interface.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:injectable/injectable.dart';

/// Dependency injection module for repository interfaces and implementations
/// This module provides the repository layer abstractions for Phase 1
@module
abstract class RepositoryModules {
  
  /// Local match repository implementation
  /// Handles file-based match operations
  @Named('local')
  @Singleton()
  MatchRepositoryInterface localMatchRepository(LoggingUseCase loggingUseCase) {
    return LocalMatchRepository(loggingUseCase.getRemoteLogger('LocalMatchRepository'));
  }

  /// Network match repository implementation
  /// Handles server-based match operations
  @Named('network')
  @preResolve
  @Singleton()
  @Scope(staticServerConnected)
  Future<MatchRepositoryInterface> networkMatchRepository(
    ServerRepository serverRepository,
    LoggingUseCase loggingUseCase,
  ) async {
    return NetworkMatchRepository(
      serverRepository,
      loggingUseCase.getRemoteLogger('NetworkMatchRepository'),
    );
  }

  /// File-based match persistence repository
  /// Handles saving/loading matches to/from files
  @Singleton()
  MatchPersistenceRepositoryInterface fileMatchPersistenceRepository(
    LoggingUseCase loggingUseCase,
  ) {
    return FileMatchRepository(loggingUseCase.getRemoteLogger('FileMatchRepository'));
  }

  /// Repository-based local match selection use case
  /// Uses the local match repository interface
  @Named('repositoryLocal')
  @Singleton()
  MatchSelectionUseCase repositoryBasedLocalMatchSelectionUseCase(
    @Named('local') MatchRepositoryInterface localMatchRepository,
    LoggingUseCase loggingUseCase,
  ) {
    return RepositoryBasedMatchSelectionUseCase(
      localMatchRepository,
      loggingUseCase.getRemoteLogger('RepositoryBasedLocalMatchSelectionUseCase'),
    );
  }

  /// Repository-based network match selection use case
  /// Uses the network match repository interface
  @Named('repositoryNetwork')
  @preResolve
  @Singleton()
  @Scope(staticServerConnected)
  Future<MatchSelectionUseCase> repositoryBasedNetworkMatchSelectionUseCase(
    @Named('network') MatchRepositoryInterface networkMatchRepository,
    LoggingUseCase loggingUseCase,
  ) async {
    return RepositoryBasedMatchSelectionUseCase(
      networkMatchRepository,
      loggingUseCase.getRemoteLogger('RepositoryBasedNetworkMatchSelectionUseCase'),
    );
  }

  // ========================================================================
  // PHASE 2: CONSOLIDATED MATCH MANAGEMENT BLOC
  // ========================================================================

  /// Consolidated MatchManagementBloc that replaces individual BLoCs
  /// Uses repository interfaces for data access
  @Singleton()
  MatchManagementBloc matchManagementBloc(
    GameConfigUseCase gameConfigUseCase,
    LoggingUseCase loggingUseCase,
  ) {
    return MatchManagementBloc(
      gameConfigUseCase,
      loggingUseCase.getRemoteLogger('MatchManagementBloc'),
    );
  }
}
