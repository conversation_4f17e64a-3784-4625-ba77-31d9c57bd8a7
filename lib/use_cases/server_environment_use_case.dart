import 'dart:io';
import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_config.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:get_it/get_it.dart';

class ServerEnvironmentUseCase {
  ServerEnvironmentUseCase();
  /// Config file path constants
  static const String _assetConfigFilePath = 'assets/config/server_config.json';
  static const String _writableConfigFilePath = 'server_config.json';

  Future<ServerEnvironmentConfig> loadServerEnvironmentConfig(
      [String? path]) async {
    // Try writable config first, then fall back to asset config
    final configPath = path ?? _writableConfigFilePath;

    print('SERVER_ENVIRONMENT_USE_CASE: Attempting to load config from $configPath');

    try {
      // Check if writable config exists
      final file = File(configPath);
      if (await file.exists()) {
        print('SERVER_ENVIRONMENT_USE_CASE: Found writable config at $configPath');
        final configFile = await JsonReadWriteDataService.parseServerConfigsFromJsonFile(configPath);
        print('SERVER_ENVIRONMENT_USE_CASE: Successfully loaded writable config with ${configFile.profiles.length} profiles');
        return configFile;
      }
    } catch (e) {
      print('SERVER_ENVIRONMENT_USE_CASE: Error loading writable config: $e');
    }

    // Fall back to asset config
    print('SERVER_ENVIRONMENT_USE_CASE: Falling back to asset config at $_assetConfigFilePath');
    try {
      final configFile = await JsonReadWriteDataService.parseServerConfigsFromJsonAsset(_assetConfigFilePath);
      print('SERVER_ENVIRONMENT_USE_CASE: Successfully loaded asset config with ${configFile.profiles.length} profiles');
      return configFile;
    } catch (e) {
      print('SERVER_ENVIRONMENT_USE_CASE: Error loading asset config: $e');
      // Return default config with empty profiles
      print('SERVER_ENVIRONMENT_USE_CASE: Returning empty default config');
      return const ServerEnvironmentConfig();
    }
  }

  /// Save server environment config to JSON file
  Future<void> saveServerEnvironmentConfig(ServerEnvironmentConfig config, [String? path]) async {
    final configPath = path ?? _writableConfigFilePath;
    try {
      await JsonReadWriteDataService.writeJsonToFile(configPath, config.toJson());
      print('SERVER_ENVIRONMENT_USE_CASE: Server config saved to $configPath');
    } catch (e) {
      print('SERVER_ENVIRONMENT_USE_CASE: Error saving server config: $e');
      throw Exception('Failed to save server config: $e');
    }
  }

  Future<bool> testConnection() async {
    try {
      /// TODO: / Important NOTE!!! the serverRepo needs this useCase for config; cannot be imported here -- need to CLEAN this up
      final serverRepository = GetIt.I<ServerRepository>();
      await serverRepository.testConnection();
      return true;
    } catch (e) {
      print('SERVER_ENVIRONMENT_USE_CASE: Error testing connection: $e');
      return false;
    }
  }
}
