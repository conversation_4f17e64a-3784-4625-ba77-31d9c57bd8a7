import 'package:common/models/game_match.dart';

import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_event.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_state.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_state.dart';
import 'package:dauntless/frameworks/network/websocket_manager.dart';
import 'package:dauntless/frameworks/network/websocket_state.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

/// Panel that displays the list of available matches to join
/// Now uses the consolidated MatchManagementBloc instead of separate BLoCs
class OpenMatchesPanel extends StatelessWidget {
  final String sourceName;
  final List<GameMatch> matches;
  final bool isLoading;
  final String? errorMessage;

  const OpenMatchesPanel({
    super.key,
    required this.sourceName,
    required this.matches,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24), // Add spacing between sections
      child: BlocBuilder<MatchManagementBloc, MatchManagementState>(
        builder: (context, state) {
          // Get current matches from BLoC state instead of using passed parameter
          final currentMatches = state.matchesBySource[sourceName] ?? [];
          final currentIsLoading = state.processingStatus == ProcessingStatus.loading;
          final currentErrorMessage = state.errorMessage;

          if (currentIsLoading) {
            return _buildLoadingState();
          }

          // For network sources, check if we should show network disconnection error
          if (_isNetworkSource(sourceName)) {
            return BlocBuilder<WebSocketManager, WebSocketState>(
              bloc: GetIt.I<WebSocketManager>(),
              builder: (context, webSocketState) {
                final isConnected = webSocketState.connectionStatus == WebSocketConnectionStatus.connected;

                if (!isConnected) {
                  return _buildNetworkDisconnectedState(context);
                }

                // Network is connected, continue with normal flow
                return _buildConnectedSourceContent(context, state, currentMatches, currentErrorMessage);
              },
            );
          }

          // For local sources, always continue with normal flow
          return _buildConnectedSourceContent(context, state, currentMatches, currentErrorMessage);
        },
      ),
    );
  }

  /// Build content for connected sources (both local and connected network sources)
  Widget _buildConnectedSourceContent(BuildContext context, MatchManagementState state,
      List<GameMatch> currentMatches, String? currentErrorMessage) {
    // For other errors (both local and network), show the error state
    if (currentErrorMessage != null) {
      return _buildErrorState(context, currentErrorMessage);
    }

    if (currentMatches.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildMatchList(context, state, currentMatches);
  }

  /// Check if the source is a network source
  bool _isNetworkSource(String sourceName) {
    return sourceName.toLowerCase().contains('network') ||
           sourceName.toLowerCase().contains('server');
  }



  /// Handle retry connection for network sources
  void _retryNetworkConnection(BuildContext context) {
    if (!_isNetworkSource(sourceName)) {
      return; // Only retry for network sources
    }

    // Show reconnecting feedback using GetIt instead of context.read
    final serverState = GetIt.I<ServerEnvironmentManager>().state;
    final activeProfile = serverState.profile?.name;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Reconnecting to ${activeProfile ?? "server"}...'),
        duration: const Duration(seconds: 2),
      ),
    );

    // Use ServerEnvironmentManager for proper reconnection
    GetIt.I<ServerEnvironmentManager>().add(ConnectToServerEvent());
  }

  /// Build source header with name and controls
  Widget _buildSourceHeader() {
    return Row(
      children: [
        const SizedBox(width: 16),
        Icon(
          Icons.source_outlined,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          sourceName,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        const SizedBox(width: 16),
      ],
    );
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSourceHeader(),
        const SizedBox(height: 16),
        const Center(
          child: Column(
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading matches...'),
            ],
          ),
        ),
      ],
    );
  }

  /// Build error state
  Widget _buildErrorState(BuildContext context, String error) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSourceHeader(),
        const SizedBox(height: 16),
        Center(
          child: Column(
            children: [
              Icon(Icons.error_outline, color: Colors.red[400], size: 48),
              const SizedBox(height: 16),
              Text(
                'Error loading matches',
                style: TextStyle(color: Colors.red[400], fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                error,
                style: TextStyle(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  context.read<MatchManagementBloc>().add(
                    RefreshMatchesFromSourceEvent(sourceName),
                  );
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build the empty state when no matches are available
  Widget _buildEmptyState(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSourceHeader(),
        const SizedBox(height: 16),
        const Center(
          child: Column(
            children: [
              Text('No open matches available'),
              SizedBox(height: 16),
            ],
          ),
        ),
        Center(
          child: ElevatedButton(
            onPressed: () {
              context.read<MatchManagementBloc>().add(const StartMatchCreationEvent());
            },
            child: const Text('Create Match'),
          ),
        ),
      ],
    );
  }

  /// Build network disconnected state for network sources
  Widget _buildNetworkDisconnectedState(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSourceHeader(),
        const SizedBox(height: 16),
        Center(
          child: Column(
            children: [
              Icon(Icons.wifi_off, color: Colors.red[400], size: 48),
              const SizedBox(height: 16),
              Text(
                'Network disconnected',
                style: TextStyle(color: Colors.red[400], fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Cannot load matches while disconnected from server',
                style: TextStyle(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              // Add server profile information
              BlocBuilder<ServerEnvironmentManager, ServerEnvironmentState>(
                bloc: GetIt.I<ServerEnvironmentManager>(),
                builder: (context, serverState) {
                  final profile = serverState.profile;
                  if (profile != null) {
                    return Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.cloud_outlined,
                              size: 14,
                              color: Colors.grey[500],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              profile.name,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[500],
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          '${profile.domain}:${profile.port}',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _retryNetworkConnection(context),
                child: const Text('Retry Connection'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build the list of available matches
  Widget _buildMatchList(BuildContext context, MatchManagementState state,
      List<GameMatch> matches) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Open Matches',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.source_outlined,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Source: $sourceName',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  // Add server information for network sources
                  if (sourceName.toLowerCase() == 'network')
                    BlocBuilder<ServerEnvironmentManager, ServerEnvironmentState>(
                      bloc: GetIt.I<ServerEnvironmentManager>(),
                      builder: (context, serverState) {
                        final profile = serverState.profile;
                        if (profile != null) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 2),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.cloud_outlined,
                                      size: 12,
                                      color: Colors.grey[500],
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      profile.name,
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Colors.grey[500],
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 16),
                                  child: Text(
                                    '${profile.domain}:${profile.port}',
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Colors.grey[600],
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                ],
              ),
              _buildSourceControls(context, matches),
            ],
          ),
        ),
        ...matches.map((match) => _buildMatchTile(context, state, match)),
        _buildCreateMatchSection(context),
      ],
    );
  }

  /// Build a single match tile
  Widget _buildMatchTile(
      BuildContext context, MatchManagementState state, GameMatch match) {
    // Check if this match is selected in MatchManagementBloc
    final bool isSelected = state.selectedMatch?.id == match.id;

    // Get current user ID
    final userId = context.watch<UserManager>().state.user?.id;

    // Check if user has already joined this match
    final bool hasJoined = userId != null &&
        match.playerSlots.any((slot) => slot.playerId == userId);

    final matchName = match.gameName ?? 'Match ${match.id}';

    // Get player count and max players from match data
    final playerCount = match.playerSlots
        .where((slot) => slot.playerId != null && slot.playerId!.isNotEmpty)
        .length;
    final maxPlayers = match.playerSlots.length;
    // final maxHumanPlayers = match.playerSlots
    //     .where((slot) => slot.type.isHuman)
    //     .length;
    final maxHumanNetworkPlayers = match.playerSlots
        .where((slot) => !slot.type.isLocal && slot.type.isHuman)
        .length;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      elevation: isSelected ? 4 : (hasJoined ? 3 : 1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(
          color: isSelected
              ? Colors.blue
              : (hasJoined ? Colors.green : Colors.transparent),
          width: isSelected || hasJoined ? 2.0 : 1.0,
        ),
      ),
      child: Column(
        children: [
          ListTile(
            title: Text(
              matchName,
              style: TextStyle(
                fontWeight: isSelected || hasJoined
                    ? FontWeight.bold
                    : FontWeight.normal,
                color: isSelected
                    ? Colors.blue
                    : (hasJoined ? Colors.green : null),
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                Text('Game type: ${match.gameTypeId}'),
                Spacer(),
                Text(match.id),
        ]),
        Row(
                  children: [
                    Icon(
                      Icons.people,
                      size: 16,
                      color: isSelected
                          ? Colors.blue
                          : (hasJoined ? Colors.green : null),
                    ),
                    Text(' $playerCount / $maxHumanNetworkPlayers ($maxPlayers)'),
                  ],
                ),
                if (isSelected && hasJoined)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Row(
                      children: const [
                        Icon(Icons.check_circle, size: 16, color: Colors.blue),
                        SizedBox(width: 4),
                        Text('Selected and Joined',
                            style: TextStyle(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  )
                else if (isSelected)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Row(
                      children: const [
                        Icon(Icons.check_circle, size: 16, color: Colors.blue),
                        SizedBox(width: 4),
                        Text('Selected',
                            style: TextStyle(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  )
                else if (hasJoined)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Row(
                      children: const [
                        Icon(Icons.check_circle, size: 16, color: Colors.green),
                        SizedBox(width: 4),
                        Text('Joined',
                            style: TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
              ],
            ),
            selected: isSelected,
            onTap: () =>
                context.read<MatchManagementBloc>().add(SelectMatchEvent(match)),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(8.0, 0.0, 8.0, 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                BlocBuilder<UserManager, UserState>(
                  builder: (context, userState) {
                    final bool hasUserId = userState.user?.id != null &&
                        userState.user!.id.isNotEmpty;

                    // Always require a user ID for join/leave actions
                    final bool hasNoUserId = !hasUserId;

                    // Button text and action changes based on join status
                    final String buttonText = hasJoined ? 'LEAVE' : 'JOIN';

                    return TextButton(
                      style: ButtonStyle(
                        foregroundColor: WidgetStateProperty.all(
                          hasJoined ? Colors.red : Colors.blue,
                        ),
                      ),
                      onPressed: hasNoUserId
                          ? null // Disable button if no user ID
                          : () {
                              if (hasJoined) {
                                // If already joined, trigger leave action
                                context
                                    .read<MatchManagementBloc>()
                                    .add(LeaveMatchEvent(match.id, playerId: userId));
                              } else {
                                // If not joined, trigger join action
                                context
                                    .read<MatchManagementBloc>()
                                    .add(JoinMatchEvent(match.id, playerId: userId));
                              }
                            },
                      child: Text(buttonText),
                    );
                  },
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    _confirmDeleteMatch(context, match);
                  },
                  child: const Text('DELETE'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Show a confirmation dialog and delete the match if confirmed
  void _confirmDeleteMatch(BuildContext context, GameMatch match) {
    final matchName = match.gameName ?? 'Match ${match.id}';

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Match'),
        content: Text(
            'Are you sure you want to delete "$matchName"? This action cannot be undone.'),
        actions: [
          TextButton(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(dialogContext).pop(),
          ),
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
            onPressed: () {
              // Close the dialog
              Navigator.of(dialogContext).pop();

              // Delete the match
              context
                  .read<MatchManagementBloc>()
                  .add(DeleteMatchEvent(match.id));

              // Show a snackbar to indicate deletion
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Deleting match "$matchName"...'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Build source controls (refresh, close buttons) with network connection awareness
  Widget _buildSourceControls(BuildContext context, List<GameMatch> matches) {
    if (_isNetworkSource(sourceName)) {
      return BlocBuilder<WebSocketManager, WebSocketState>(
        bloc: GetIt.I<WebSocketManager>(),
        builder: (context, webSocketState) {
          final isConnected = webSocketState.connectionStatus == WebSocketConnectionStatus.connected;

          return Row(
            children: [
              Text('${matches.length} matches'),
              const SizedBox(width: 16),
              if (!isConnected) ...[
                // Show network disconnected state with retry button
                Icon(Icons.wifi_off, color: Colors.red[400], size: 20),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _retryNetworkConnection(context),
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('Retry'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red[400],
                  ),
                ),
              ] else ...[
                // Show normal refresh button when connected
                IconButton(
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh matches',
                  onPressed: () {
                    context.read<MatchManagementBloc>().add(
                      RefreshMatchesFromSourceEvent(sourceName),
                    );
                  },
                ),
              ],
              IconButton(
                icon: const Icon(Icons.close),
                tooltip: 'Remove match source',
                onPressed: () {
                  context.read<MatchManagementBloc>().add(
                    RemoveMatchSourceEvent(sourceName),
                  );
                },
              ),
            ],
          );
        },
      );
    }

    // For local sources, always show normal controls
    return Row(
      children: [
        Text('${matches.length} matches'),
        const SizedBox(width: 16),
        IconButton(
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh matches',
          onPressed: () {
            context.read<MatchManagementBloc>().add(
              RefreshMatchesFromSourceEvent(sourceName),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.close),
          tooltip: 'Remove match source',
          onPressed: () {
            context.read<MatchManagementBloc>().add(
              RemoveMatchSourceEvent(sourceName),
            );
          },
        ),
      ],
    );
  }

  /// Build create match section with network connection awareness
  Widget _buildCreateMatchSection(BuildContext context) {
    if (_isNetworkSource(sourceName)) {
      return BlocBuilder<WebSocketManager, WebSocketState>(
        bloc: GetIt.I<WebSocketManager>(),
        builder: (context, webSocketState) {
          final isConnected = webSocketState.connectionStatus == WebSocketConnectionStatus.connected;

          if (!isConnected) {
            // Don't show create match button for disconnected network sources
            return const SizedBox.shrink();
          }

          // Show create match button for connected network sources
          return _CreateMatchButton();
        },
      );
    }

    // Show create match button for local sources
    return _CreateMatchButton();
  }
}

/// Start match button widget
class _CreateMatchButton extends StatelessWidget {
  const _CreateMatchButton();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          backgroundColor: Colors.blue,
        ),
        onPressed: () {
          // Switch to match creation mode
          context.read<MatchManagementBloc>().add(const StartMatchCreationEvent());
        },
        child: const Text(
          'Create Match',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
