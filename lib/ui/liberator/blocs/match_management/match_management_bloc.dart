import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import 'package:common/models/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';

import 'package:dauntless/models/base/game_match.dart';
import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/interfaces/match_source_repository_interface.dart';

import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'match_management_event.dart';
import 'match_management_state.dart';

/// Consolidated BLoC for match management
/// Combines functionality from MatchSelectionBloc, CreateMatchBloc, and MatchSelectionEnvironmentManager
/// Uses repository interfaces from Phase 1 for data access
class MatchManagementBloc extends Bloc<MatchManagementEvent, MatchManagementState> {
  // ========================================================================
  // DEPENDENCIES
  // ========================================================================
  
  final GameConfigUseCase _gameConfigUseCase;
  final RemoteLogger _logger;
  
  // Repository interfaces for different data sources
  final Map<String, MatchRepositoryInterface> _matchRepositories = {};
  final Map<String, MatchSourceRepositoryInterface> _sourceRepositories = {};
  // Removed unused _persistenceRepository field
  
  // Stream subscriptions for real-time updates
  final Map<String, StreamSubscription> _sourceSubscriptions = {};

  // WebSocket integration for real-time match updates
  StreamSubscription<List<GameMatch>>? _openMatchesSubscription;
  
  // ========================================================================
  // CONSTRUCTOR
  // ========================================================================
  
  MatchManagementBloc(
    this._gameConfigUseCase,
    this._logger,
  ) : super(const MatchManagementState()) {
    // Initialize event handlers
    _registerEventHandlers();

    // Don't initialize repositories in constructor - do it lazily when needed
    // This prevents dependency injection order issues
  }

  // ========================================================================
  // EVENT HANDLER REGISTRATION
  // ========================================================================
  
  void _registerEventHandlers() {
    // Initialization events
    on<InitializeMatchManagementEvent>(_onInitialize);
    on<LoadAvailableMatchSourcesEvent>(_onLoadAvailableMatchSources);
    
    // Match discovery & selection events
    on<LoadMatchDataEvent>(_onLoadMatchData);
    on<RefreshMatchesFromSourceEvent>(_onRefreshMatchesFromSource);
    on<SelectMatchEvent>(_onSelectMatch);
    on<ClearSelectedMatchEvent>(_onClearSelectedMatch);
    
    // Match joining events
    on<JoinSelectedMatchEvent>(_onJoinSelectedMatch);
    on<JoinMatchEvent>(_onJoinMatch);
    on<LeaveMatchEvent>(_onLeaveMatch);
    
    // Match creation events
    on<StartMatchCreationEvent>(_onStartMatchCreation);
    on<CancelMatchCreationEvent>(_onCancelMatchCreation);
    on<SelectMatchConfigEvent>(_onSelectMatchConfig);
    on<UpdateGameNameEvent>(_onUpdateGameName);
    
    // Player slot management events
    on<AddPlayerSlotEvent>(_onAddPlayerSlot);
    on<RemovePlayerSlotEvent>(_onRemovePlayerSlot);
    on<UpdatePlayerTypeEvent>(_onUpdatePlayerType);
    on<UpdateSelectedMatchPlayerTypeEvent>(_onUpdateSelectedMatchPlayerType);
    on<UpdatePlayerNameEvent>(_onUpdatePlayerName);
    on<SetHostPlayerEvent>(_onSetHostPlayer);
    on<JoinPlayerSlotEvent>(_onJoinPlayerSlot);
    on<JoinSelectedMatchSlotEvent>(_onJoinSelectedMatchSlot);
    
    // Match lifecycle events
    on<CreateAndStartMatchEvent>(_onCreateAndStartMatch);
    on<DeleteMatchEvent>(_onDeleteMatch);
    
    // Real-time monitoring events
    on<SubscribeToMatchUpdatesEvent>(_onSubscribeToMatchUpdates);
    on<UnsubscribeFromMatchUpdatesEvent>(_onUnsubscribeFromMatchUpdates);
    
    // Source management events
    on<AddMatchSourceEvent>(_onAddMatchSource);
    on<RemoveMatchSourceEvent>(_onRemoveMatchSource);
    
    // Network & capability events
    on<NetworkCapabilityChangedEvent>(_onNetworkCapabilityChanged);
    
    // UI state events
    on<ToggleServerProfileSelectorEvent>(_onToggleServerProfileSelector);
  }

  // ========================================================================
  // REPOSITORY INITIALIZATION
  // ========================================================================

  /// Ensure repositories are initialized (lazy initialization)
  void _ensureRepositoriesInitialized() {
    if (_matchRepositories.isEmpty) {
      _initializeRepositories();
    }
  }
  
  void _initializeRepositories() {
    try {
      // Clear existing repositories to prevent duplicates
      _matchRepositories.clear();

      // Get local repository (should always be available)
      try {
        final localRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'local');
        _matchRepositories['local'] = localRepo;
        _logger.info('Initialized local match repository');
      } catch (e) {
        _logger.error('Failed to get local repository: $e');
        // Don't throw - continue without local repository
      }

      // Try to get network repository (only available when server connected)
      _logger.info('Checking for network repository, current scope: ${GetIt.I.currentScopeName}');
      if (GetIt.I.currentScopeName == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          _matchRepositories['network'] = networkRepo;
          _logger.info('Initialized network match repository');
        } catch (e) {
          _logger.warn('Network match repository not available: $e');
        }
      } else {
        _logger.info('Network repository not available - not in server-connected scope');
      }

    } catch (e) {
      _logger.error('Failed to initialize repositories: $e');
    }
  }

  // ========================================================================
  // INITIALIZATION EVENT HANDLERS
  // ========================================================================
  
  Future<void> _onInitialize(
    InitializeMatchManagementEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      _logger.info('Initializing MatchManagementBloc');

      // Ensure repositories are initialized
      _ensureRepositoriesInitialized();

      // Load available game configurations
      final configs = await _gameConfigUseCase.loadLocalGameConfigs();

      // Get available match sources
      final availableSources = _matchRepositories.keys.toList();
      
      // Check network capability and add network sources if available
      final hasNetworkCapability = await _checkAndAddNetworkSources();

      emit(state.copyWith(
        availableConfigs: configs,
        availableMatchSources: _matchRepositories.keys.toList(),
        hasNetworkCapability: hasNetworkCapability,
        processingStatus: ProcessingStatus.loaded,
      ));
      
      _logger.info('MatchManagementBloc initialized with ${configs.length} configs and ${availableSources.length} sources');
      
    } catch (e) {
      _logger.error('Failed to initialize MatchManagementBloc: $e');
      emit(state.setError('Failed to initialize: $e'));
    }
  }

  Future<void> _onLoadAvailableMatchSources(
    LoadAvailableMatchSourcesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    try {
      final availableSources = _matchRepositories.keys.toList();
      emit(state.copyWith(availableMatchSources: availableSources));
      _logger.info('Loaded ${availableSources.length} available match sources');
    } catch (e) {
      _logger.error('Failed to load available match sources: $e');
      emit(state.setError('Failed to load match sources: $e'));
    }
  }

  // ========================================================================
  // MATCH DISCOVERY & SELECTION EVENT HANDLERS
  // ========================================================================
  
  Future<void> _onLoadMatchData(
    LoadMatchDataEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      // Ensure repositories are initialized
      _ensureRepositoriesInitialized();

      final gameName = event.gameName ?? 'default'; // TODO: Get from current game context
      _logger.info('Loading match data for game: $gameName');
      
      final allMatches = <GameMatch>[];
      final matchesBySource = <String, List<GameMatch>>{};
      
      // Load matches from all available repositories with deduplication
      final seenMatchIds = <String>{};
      final networkMatches = <GameMatch>[];

      // Create a snapshot of repositories to avoid concurrent modification during iteration
      final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);

      for (final entry in repositoriesSnapshot.entries) {
        final sourceName = entry.key;
        final repository = entry.value;

        try {
          final matches = await repository.fetchOpenMatches(gameName);

          // Deduplicate matches by ID
          final uniqueMatches = matches.where((match) {
            if (seenMatchIds.contains(match.id)) {
              _logger.info('Skipping duplicate match ${match.id} from $sourceName');
              return false;
            }
            seenMatchIds.add(match.id);
            return true;
          }).toList();

          allMatches.addAll(uniqueMatches);

          // Consolidate network sources under a single 'network' key
          if (sourceName.toLowerCase() == 'network') {
            networkMatches.addAll(uniqueMatches);
            _logger.info('Added ${uniqueMatches.length} unique matches to consolidated network source (${matches.length} total from $sourceName)');
          } else {
            matchesBySource[sourceName] = uniqueMatches;
            _logger.info('Loaded ${uniqueMatches.length} unique matches from $sourceName (${matches.length} total)');
          }
        } catch (e) {
          _logger.warn('Failed to load matches from $sourceName: $e');
          // For local sources, always provide empty list (graceful degradation)
          // For network sources, only provide empty list if it's not the main network source
          if (_isLocalSource(sourceName) || sourceName.toLowerCase() != 'network') {
            matchesBySource[sourceName] = [];
          }
        }
      }

      // Add consolidated network matches if any were found
      if (networkMatches.isNotEmpty || repositoriesSnapshot.keys.any((key) => key.toLowerCase() == 'network')) {
        matchesBySource['network'] = networkMatches;
        _logger.info('Consolidated network source contains ${networkMatches.length} total matches');
      }
      
      emit(state.copyWith(
        openMatches: allMatches,
        matchesBySource: matchesBySource,
        processingStatus: ProcessingStatus.loaded,
      ));
      
      _logger.info('Loaded total of ${allMatches.length} matches from ${matchesBySource.length} sources');
      
    } catch (e) {
      _logger.error('Failed to load match data: $e');
      emit(state.setError('Failed to load match data: $e'));
    }
  }

  Future<void> _onRefreshMatchesFromSource(
    RefreshMatchesFromSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final sourceName = event.sourceName;
    final repository = _matchRepositories[sourceName];

    if (repository == null) {
      _logger.warn('Repository not found for source: $sourceName');
      return;
    }

    try {
      final gameName = 'default'; // TODO: Get from current game context
      final matches = await repository.fetchOpenMatches(gameName);

      final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
      updatedMatchesBySource[sourceName] = matches;

      // Rebuild the complete matches list
      final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

      emit(state.copyWith(
        openMatches: allMatches,
        matchesBySource: updatedMatchesBySource,
        errorMessage: null, // Clear any previous errors on successful refresh
      ));

      _logger.info('Refreshed ${matches.length} matches from $sourceName');

    } catch (e) {
      _logger.error('Failed to refresh matches from $sourceName: $e');

      // For local sources, handle errors gracefully by showing empty state
      if (_isLocalSource(sourceName)) {
        _logger.info('Handling local source error gracefully, showing empty state');
        final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
        updatedMatchesBySource[sourceName] = []; // Show empty list for local sources

        // Rebuild the complete matches list
        final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

        emit(state.copyWith(
          openMatches: allMatches,
          matchesBySource: updatedMatchesBySource,
          errorMessage: null, // Don't show error for local sources
        ));
      } else {
        // For network sources, show the error
        emit(state.setError('Failed to refresh matches from $sourceName: $e'));
      }
    }
  }

  void _onSelectMatch(
    SelectMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Match selected: ${event.match.id}');
    
    emit(state.copyWith(
      selectedMatch: event.match,
      currentMode: 'selection',
    ));
  }

  void _onClearSelectedMatch(
    ClearSelectedMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Clearing selected match: ${event.matchId ?? 'current'}');
    
    emit(state.copyWith(
      selectedMatch: null,
    ));
  }

  // ========================================================================
  // MATCH JOINING EVENT HANDLERS
  // ========================================================================

  Future<void> _onJoinSelectedMatch(
    JoinSelectedMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    if (state.selectedMatch == null) {
      emit(state.setError('No match selected'));
      return;
    }

    emit(state.setLoading());

    try {
      final match = state.selectedMatch!;
      final success = await _joinMatchById(match.id);

      if (success) {
        _logger.info('Successfully joined match ${match.id}');
        emit(state.setLoaded());
      } else {
        emit(state.setError('Failed to join match'));
      }
    } catch (e) {
      _logger.error('Error joining selected match: $e');
      emit(state.setError('Error joining match: $e'));
    }
  }

  Future<void> _onJoinMatch(
    JoinMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      final success = await _joinMatchById(event.matchId, playerId: event.playerId);

      if (success) {
        _logger.info('✅ JOIN SUCCESS: Joined match ${event.matchId}, waiting for WebSocket update...');
        emit(state.setLoaded());
      } else {
        _logger.error('❌ JOIN FAILED: Could not join match ${event.matchId}');
        emit(state.setError('Failed to join match'));
      }
    } catch (e) {
      _logger.error('Error joining match ${event.matchId}: $e');
      emit(state.setError('Error joining match: $e'));
    }
  }

  Future<void> _onLeaveMatch(
    LeaveMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      final success = await _leaveMatchById(event.matchId, playerId: event.playerId);

      if (success) {
        _logger.info('Successfully left match ${event.matchId}');
        emit(state.setLoaded());
      } else {
        emit(state.setError('Failed to leave match'));
      }
    } catch (e) {
      _logger.error('Error leaving match ${event.matchId}: $e');
      emit(state.setError('Error leaving match: $e'));
    }
  }

  // ========================================================================
  // MATCH CREATION EVENT HANDLERS
  // ========================================================================

  void _onStartMatchCreation(
    StartMatchCreationEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Starting match creation');

    // Create default player slots based on available game configs
    List<PlayerSlot> defaultPlayerSlots = [];

    // If we have available configs, use the first one to create proper slots
    if (state.availableConfigs.isNotEmpty) {
      final defaultConfig = state.availableConfigs.first;

      // Create slots based on the player classes in the config
      for (int i = 0; i < defaultConfig.playerClasses.length && i < 4; i++) {
        final playerClass = defaultConfig.playerClasses[i];
        defaultPlayerSlots.add(PlayerSlot(
          id: 'slot_$i',
          name: playerClass.defaultPlayerName ?? playerClass.name,
          type: PlayerType.humanLocal,
          playerClassId: playerClass.id,
        ));
      }

      // Set the default config as selected
      emit(state.enterCreationMode().copyWith(
        selectedConfigId: defaultConfig.id,
        gameName: defaultConfig.name,
        matchId: null,
        playerSlots: defaultPlayerSlots,
      ));
    } else {
      // Fallback if no configs available
      defaultPlayerSlots = [
        PlayerSlot(
          id: 'slot_0',
          name: 'Player 1',
          type: PlayerType.humanLocal,
          playerClassId: 'default',
        ),
        PlayerSlot(
          id: 'slot_1',
          name: 'Player 2',
          type: PlayerType.humanLocal,
          playerClassId: 'default',
        ),
      ];

      emit(state.enterCreationMode().copyWith(
        selectedConfigId: null,
        gameName: null,
        matchId: null,
        playerSlots: defaultPlayerSlots,
      ));
    }
  }

  void _onCancelMatchCreation(
    CancelMatchCreationEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Cancelling match creation');

    emit(state.enterSelectionMode().copyWith(
      selectedConfigId: null,
      gameName: null,
      matchId: null,
      playerSlots: [],
    ));
  }

  void _onSelectMatchConfig(
    SelectMatchConfigEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Selected match config: ${event.matchConfigId}');

    emit(state.copyWith(
      selectedConfigId: event.matchConfigId,
    ));
  }

  void _onUpdateGameName(
    UpdateGameNameEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    emit(state.copyWith(gameName: event.gameName));
  }

  // ========================================================================
  // PLAYER SLOT MANAGEMENT EVENT HANDLERS
  // ========================================================================

  void _onAddPlayerSlot(
    AddPlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final newSlot = PlayerSlot(
      id: 'slot_${state.playerSlots.length}',
      name: 'Player ${state.playerSlots.length + 1}',
      type: PlayerType.humanLocal,
      playerClassId: 'default_player_class', // TODO: Get from game config
    );

    final updatedSlots = [...state.playerSlots, newSlot];
    emit(state.copyWith(playerSlots: updatedSlots));

    _logger.info('Added player slot: ${newSlot.id}');
  }

  void _onRemovePlayerSlot(
    RemovePlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    if (event.slotIndex < 0 || event.slotIndex >= state.playerSlots.length) {
      _logger.warn('Invalid slot index for removal: ${event.slotIndex}');
      return;
    }

    final updatedSlots = [...state.playerSlots];
    final removedSlot = updatedSlots.removeAt(event.slotIndex);

    emit(state.copyWith(playerSlots: updatedSlots));

    _logger.info('Removed player slot: ${removedSlot.id}');
  }

  Future<void> _onUpdatePlayerType(
    UpdatePlayerTypeEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    if (event.slotIndex < 0 || event.slotIndex >= state.playerSlots.length) {
      _logger.warn('Invalid slot index for type update: ${event.slotIndex}');
      return;
    }

    final updatedSlots = [...state.playerSlots];
    final oldSlot = updatedSlots[event.slotIndex];
    updatedSlots[event.slotIndex] = oldSlot.copyWith(
      type: event.playerType,
    );

    // Emit initial state change to show updated type immediately
    emit(state.copyWith(
      playerSlots: updatedSlots,
      processingStatus: ProcessingStatus.loading,
    ));

    _logger.info('Updated player type for slot ${event.slotIndex}: ${event.playerType}');

    // If player type is humanNetwork and we haven't created a server match yet,
    // we need to create one
    bool needsServerMatch = event.playerType == PlayerType.humanNetwork &&
        state.matchId == null &&
        state.hasNetworkCapability;

    if (needsServerMatch) {
      try {
        _logger.info('Creating server match for network player...');
        final result = await _createServerMatch(updatedSlots);

        // After creating the server match, update state with new match ID and selected match
        emit(state.copyWith(
          matchId: result.matchId,
          selectedMatch: result.match,
          playerSlots: updatedSlots,
          processingStatus: ProcessingStatus.loaded,
        ));

        _logger.info('Successfully created server match: ${result.matchId}');
      } catch (e) {
        _logger.error('Failed to create server match: $e');
        emit(state.copyWith(
          playerSlots: updatedSlots,
          processingStatus: ProcessingStatus.error,
          errorMessage: 'Failed to create network match: $e',
        ));
        return;
      }
    } else if (state.matchId != null && state.hasNetworkCapability) {
      // If we already have a server match, update the player slots on the server
      try {
        await _updateServerMatchPlayerSlots(updatedSlots);
        emit(state.copyWith(
          playerSlots: updatedSlots,
          processingStatus: ProcessingStatus.loaded,
        ));
        _logger.info('Successfully updated player type on server for match ${state.matchId}');
      } catch (e) {
        _logger.error('Failed to update player type on server: $e');
        emit(state.copyWith(
          playerSlots: updatedSlots,
          processingStatus: ProcessingStatus.error,
          errorMessage: 'Failed to update server match: $e',
        ));
      }
    } else {
      // For local-only matches or no network capability
      emit(state.copyWith(
        playerSlots: updatedSlots,
        processingStatus: ProcessingStatus.loaded,
      ));
    }
  }

  Future<void> _onUpdateSelectedMatchPlayerType(
    UpdateSelectedMatchPlayerTypeEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final selectedMatch = state.selectedMatch;
    if (selectedMatch == null) {
      _logger.warn('No selected match to update player type');
      return;
    }

    if (event.slotIndex < 0 || event.slotIndex >= selectedMatch.playerSlots.length) {
      _logger.warn('Invalid slot index for selected match type update: ${event.slotIndex}');
      return;
    }

    // Create updated player slots for the selected match
    final updatedSlots = [...selectedMatch.playerSlots];
    final oldSlot = updatedSlots[event.slotIndex];
    updatedSlots[event.slotIndex] = oldSlot.copyWith(
      type: event.playerType,
    );

    // Create updated match with new player slots
    final updatedMatch = selectedMatch.copyWith(
      playerSlots: updatedSlots,
    );

    // Update the state with the modified selected match
    emit(state.copyWith(
      selectedMatch: updatedMatch,
      processingStatus: ProcessingStatus.loading,
    ));

    _logger.info('Updated player type for selected match slot ${event.slotIndex}: ${event.playerType}');

    // Check if this is a network match and update the server
    final isNetworkMatch = _isNetworkMatch(selectedMatch, state);
    _logger.info('Server update check: isNetworkMatch=$isNetworkMatch');

    // Check if we can access the server repository (actual server connectivity)
    bool canUpdateServer = false;
    try {
      GetIt.I<ServerRepository>(); // Check if available
      canUpdateServer = true;
      _logger.info('ServerRepository is available for updates');
    } catch (e) {
      _logger.warn('ServerRepository not available for updates: $e');
    }

    _logger.info('Server update decision: isNetworkMatch=$isNetworkMatch, canUpdateServer=$canUpdateServer');

    if (isNetworkMatch && canUpdateServer) {
      _logger.info('Attempting server update for match ${selectedMatch.id}');
      try {
        // Get current user ID for the update
        final user = GetIt.I<UserManager>().state.user;
        if (user == null) {
          throw Exception('No user available for server update');
        }

        // Update the server with the new player slots
        final serverRepository = GetIt.I<ServerRepository>();
        final success = await serverRepository.updateMatchPlayerSlots(
          selectedMatch.id,
          updatedSlots,
          user.id,
        );

        if (success) {
          _logger.info('Successfully updated player type on server for match ${selectedMatch.id}');
          emit(state.copyWith(
            selectedMatch: updatedMatch,
            processingStatus: ProcessingStatus.loaded,
          ));
        } else {
          throw Exception('Server rejected player type update');
        }
      } catch (e) {
        _logger.error('Failed to update player type on server: $e');
        // Keep the local update but show error status
        emit(state.copyWith(
          selectedMatch: updatedMatch,
          processingStatus: ProcessingStatus.error,
          errorMessage: 'Failed to update server match: $e',
        ));
      }
    } else {
      // For local matches or when network not available, just update local state
      _logger.info('Skipping server update: local match or server unavailable');
      emit(state.copyWith(
        selectedMatch: updatedMatch,
        processingStatus: ProcessingStatus.loaded,
      ));
    }
  }

  void _onUpdatePlayerName(
    UpdatePlayerNameEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    if (event.slotIndex < 0 || event.slotIndex >= state.playerSlots.length) {
      _logger.warn('Invalid slot index for name update: ${event.slotIndex}');
      return;
    }

    final updatedSlots = [...state.playerSlots];
    updatedSlots[event.slotIndex] = updatedSlots[event.slotIndex].copyWith(
      name: event.name,
    );

    emit(state.copyWith(playerSlots: updatedSlots));

    _logger.info('Updated player name for slot ${event.slotIndex}: ${event.name}');
  }

  void _onSetHostPlayer(
    SetHostPlayerEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    // For now, we'll track the host by keeping the host slot as the first slot
    // or by using a separate hostPlayerId field in the state
    // Since PlayerSlot doesn't have isHost, we'll handle this in the UI layer

    _logger.info('Set host player: ${event.slotId}');

    // TODO: Implement host tracking logic when PlayerSlot model supports it
    // For now, just log the event
  }

  void _onJoinPlayerSlot(
    JoinPlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    if (event.slotIndex < 0 || event.slotIndex >= state.playerSlots.length) {
      _logger.warn('Invalid slot index for join: ${event.slotIndex}');
      return;
    }

    final user = GetIt.I<UserManager>().state.user;
    if (user == null) {
      emit(state.setError('No user logged in'));
      return;
    }

    final updatedSlots = [...state.playerSlots];
    updatedSlots[event.slotIndex] = updatedSlots[event.slotIndex].copyWith(
      playerId: user.id,
      name: user.id, // User model doesn't have name property, using id for now
    );

    emit(state.copyWith(playerSlots: updatedSlots));

    _logger.info('User ${user.id} joined slot ${event.slotIndex}');
  }

  /// Join the current user to a specific slot in the selected match (with server update)
  Future<void> _onJoinSelectedMatchSlot(
    JoinSelectedMatchSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    if (state.selectedMatch == null) {
      emit(state.setError('No match selected'));
      return;
    }

    final user = GetIt.I<UserManager>().state.user;
    if (user == null) {
      emit(state.setError('No user logged in'));
      return;
    }

    final selectedMatch = state.selectedMatch!;
    final currentUserId = user.id;

    if (event.slotIndex < 0 || event.slotIndex >= selectedMatch.playerSlots.length) {
      emit(state.setError('Invalid slot index: ${event.slotIndex}'));
      return;
    }

    emit(state.setLoading());

    try {
      // Create updated player slots with slot switching logic
      final updatedSlots = [...selectedMatch.playerSlots];

      // First, remove the user from any existing slots (slot switching)
      for (int i = 0; i < updatedSlots.length; i++) {
        if (updatedSlots[i].playerId == currentUserId) {
          _logger.info('Removing user $currentUserId from slot $i before joining slot ${event.slotIndex}');
          updatedSlots[i] = updatedSlots[i].copyWith(playerId: null);
        }
      }

      // Then, assign the user to the target slot
      updatedSlots[event.slotIndex] = updatedSlots[event.slotIndex].copyWith(
        playerId: currentUserId,
      );

      // Check if this is a network match and update the server
      final isNetworkMatch = _isNetworkMatch(selectedMatch, state);

      if (isNetworkMatch) {
        // Check if we can access the server repository
        bool canUpdateServer = false;
        try {
          GetIt.I<ServerRepository>(); // Check if available
          canUpdateServer = true;
        } catch (e) {
          _logger.warn('ServerRepository not available for slot join: $e');
        }

        if (canUpdateServer) {
          try {
            // Use the join match API which triggers automatic removal from other matches
            final success = await _joinMatchById(selectedMatch.id, playerId: currentUserId);

            if (success) {
              _logger.info('✅ JOIN SUCCESS: Joined match ${selectedMatch.id} via slot selection, waiting for WebSocket update...');
              emit(state.setLoaded());
            } else {
              _logger.error('❌ JOIN FAILED: Could not join match ${selectedMatch.id} via slot selection');
              emit(state.setError('Failed to join match'));
            }
          } catch (e) {
            _logger.error('Error joining match via slot selection: $e');
            emit(state.setError('Error joining match: $e'));
          }
        } else {
          emit(state.setError('Server not available for slot joining'));
        }
      } else {
        // For local matches, just update local state
        final updatedMatch = selectedMatch.copyWith(playerSlots: updatedSlots);
        emit(state.copyWith(
          selectedMatch: updatedMatch,
          processingStatus: ProcessingStatus.loaded,
        ));
        _logger.info('User $currentUserId joined slot ${event.slotIndex} in local match');
      }
    } catch (e) {
      _logger.error('Error joining selected match slot: $e');
      emit(state.setError('Error joining slot: $e'));
    }
  }

  // ========================================================================
  // MATCH LIFECYCLE EVENT HANDLERS
  // ========================================================================

  Future<void> _onCreateAndStartMatch(
    CreateAndStartMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    if (!state.isReadyToCreateMatch) {
      emit(state.setError('Match configuration incomplete'));
      return;
    }

    emit(state.setLoading());

    try {
      final user = GetIt.I<UserManager>().state.user;
      if (user == null) {
        emit(state.setError('No user logged in'));
        return;
      }

      // Create the match
      final newMatch = GameMatch(
        id: GenerateIdIfNeededConverter().fromJson(null),
        gameTypeId: state.selectedConfig!.id,
        playerSlots: state.playerSlots,
        status: event.openForJoining ? MatchStatus.open : MatchStatus.active,
        creatorId: user.id,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
        gameName: state.gameName,
      );

      // Determine which repository to use based on player types
      final hasNetworkPlayers = state.playerSlots.any((slot) =>
          slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

      final repositoryName = hasNetworkPlayers ? 'network' : 'local';
      final repository = _matchRepositories[repositoryName];

      if (repository == null) {
        emit(state.setError('$repositoryName repository not available'));
        return;
      }

      final createdMatch = await repository.createMatch(newMatch, state.selectedConfig!);

      if (createdMatch != null) {
        _logger.info('Successfully created match: ${createdMatch.id}');

        // Update state with created match
        emit(state.copyWith(
          matchId: createdMatch.id,
          selectedMatch: createdMatch,
          processingStatus: ProcessingStatus.loaded,
        ));

        // Refresh matches to show the new match
        add(const LoadMatchDataEvent());

      } else {
        emit(state.setError('Failed to create match'));
      }

    } catch (e) {
      _logger.error('Error creating match: $e');
      emit(state.setError('Error creating match: $e'));
    }
  }

  Future<void> _onDeleteMatch(
    DeleteMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      // Find which source the match belongs to
      String? matchSource;
      for (final entry in state.matchesBySource.entries) {
        final sourceName = entry.key;
        final matches = entry.value;
        if (matches.any((match) => match.id == event.matchId)) {
          matchSource = sourceName;
          break;
        }
      }

      if (matchSource == null) {
        _logger.warn('Could not find source for match ${event.matchId}');
        emit(state.setError('Match not found in any source'));
        return;
      }

      // Get the repository for the match source
      final repository = _matchRepositories[matchSource];
      if (repository == null) {
        _logger.warn('No repository found for source: $matchSource');
        emit(state.setError('No repository available for match source'));
        return;
      }

      _logger.info('Deleting match ${event.matchId} from source: $matchSource');

      // Delete from the correct repository
      final success = await repository.deleteMatch(event.matchId);

      if (success) {
        _logger.info('Successfully deleted match: ${event.matchId} from $matchSource');

        // Clear selected match if it was deleted
        if (state.selectedMatch?.id == event.matchId) {
          emit(state.copyWith(selectedMatch: null));
        }

        // Refresh matches
        add(const LoadMatchDataEvent());

      } else {
        _logger.warn('Repository reported failure to delete match ${event.matchId}');
        emit(state.setError('Failed to delete match from $matchSource'));
      }

    } catch (e) {
      _logger.error('Error deleting match ${event.matchId}: $e');
      emit(state.setError('Error deleting match: $e'));
    }
  }

  // ========================================================================
  // REAL-TIME MONITORING EVENT HANDLERS
  // ========================================================================

  Future<void> _onSubscribeToMatchUpdates(
    SubscribeToMatchUpdatesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    try {
      final gameName = event.gameName ?? 'default';

      // Subscribe to updates from sources that support real-time monitoring
      for (final entry in _sourceRepositories.entries) {
        final sourceName = entry.key;
        final sourceRepo = entry.value;

        if (sourceRepo.supportsRealTimeMonitoring) {
          await sourceRepo.startMonitoring(gameName);

          // Listen to match updates
          final subscription = sourceRepo.matchUpdates.listen(
            (matches) {
              _handleMatchUpdatesFromSource(sourceName, matches);
            },
            onError: (error) {
              _logger.error('Error in match updates from $sourceName: $error');
            },
          );

          _sourceSubscriptions[sourceName] = subscription;
          _logger.info('Subscribed to match updates from $sourceName');
        }
      }

      // Also subscribe to WebSocket updates for real-time match synchronization
      _subscribeToWebSocketUpdates();

      emit(state.copyWith(
        isSubscribedToUpdates: true,
        monitoredSources: _sourceSubscriptions.keys.toSet(),
      ));

    } catch (e) {
      _logger.error('Error subscribing to match updates: $e');
      emit(state.setError('Error subscribing to updates: $e'));
    }
  }

  Future<void> _onUnsubscribeFromMatchUpdates(
    UnsubscribeFromMatchUpdatesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    try {
      // Cancel all subscriptions and stop monitoring
      for (final entry in _sourceSubscriptions.entries) {
        await entry.value.cancel();

        final sourceRepo = _sourceRepositories[entry.key];
        if (sourceRepo != null) {
          await sourceRepo.stopMonitoring();
        }
      }

      _sourceSubscriptions.clear();

      // Also unsubscribe from WebSocket updates
      _unsubscribeFromWebSocketUpdates();

      emit(state.copyWith(
        isSubscribedToUpdates: false,
        monitoredSources: {},
      ));

      _logger.info('Unsubscribed from all match updates');

    } catch (e) {
      _logger.error('Error unsubscribing from match updates: $e');
    }
  }

  // ========================================================================
  // SOURCE MANAGEMENT EVENT HANDLERS
  // ========================================================================

  void _onAddMatchSource(
    AddMatchSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final updatedSources = [...state.availableMatchSources];
    if (!updatedSources.contains(event.sourceName)) {
      updatedSources.add(event.sourceName);
      emit(state.copyWith(availableMatchSources: updatedSources));
      _logger.info('Added match source: ${event.sourceName}');
    }
  }

  void _onRemoveMatchSource(
    RemoveMatchSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final sourceName = event.sourceName;
    _logger.info('Removing match source: $sourceName');

    // Remove from repositories
    final removedRepository = _matchRepositories.remove(sourceName);

    // Update available sources
    final updatedSources = [...state.availableMatchSources];
    updatedSources.remove(sourceName);

    // Remove matches from this source
    final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
    updatedMatchesBySource.remove(sourceName);

    // Check if this was a network source
    final wasNetworkSource = removedRepository?.supportsRealTimeUpdates ?? false;
    // Create snapshot to avoid concurrent modification during iteration
    final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
    final hasNetworkCapability = repositoriesSnapshot.values.any((repo) => repo.supportsRealTimeUpdates);

    // If we removed the last network source, disconnect from server
    if (wasNetworkSource && !hasNetworkCapability) {
      _logger.info('Removed last network source, disconnecting from server');
      _disconnectFromServer();
    }

    // Update state
    emit(state.copyWith(
      availableMatchSources: updatedSources,
      matchesBySource: updatedMatchesBySource,
      hasNetworkCapability: hasNetworkCapability,
    ));

    _logger.info('Removed match source: $sourceName, remaining sources: $updatedSources');

    // Unsubscribe from WebSocket updates if no network sources remain
    if (wasNetworkSource && !hasNetworkCapability) {
      _unsubscribeFromWebSocketUpdates();
    }
  }

  // ========================================================================
  // NETWORK & CAPABILITY EVENT HANDLERS
  // ========================================================================

  void _onNetworkCapabilityChanged(
    NetworkCapabilityChangedEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    // Check if we're losing network capability
    bool shouldClearMatchCreation = false;
    bool shouldClearSelectedMatch = false;

    if (!event.hasNetworkCapability && state.hasNetworkCapability) {
      // Check if currently creating a network match
      if (state.isCreatingMatch) {
        final hasNetworkPlayers = state.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (hasNetworkPlayers) {
          _logger.info('Clearing network match creation due to network capability loss');
          shouldClearMatchCreation = true;
        }
      }

      // Check if we have a selected network match for joining
      if (state.selectedMatch != null) {
        final selectedMatchHasNetworkPlayers = state.selectedMatch!.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (selectedMatchHasNetworkPlayers) {
          _logger.info('Clearing selected network match due to network capability loss');
          shouldClearSelectedMatch = true;
        }
      }
    }

    if (shouldClearMatchCreation) {
      // Clear match creation and return to selection mode
      emit(state.enterSelectionMode().copyWith(
        hasNetworkCapability: event.hasNetworkCapability,
        selectedConfigId: null,
        gameName: null,
        matchId: null,
        playerSlots: [],
        selectedMatch: shouldClearSelectedMatch ? null : state.selectedMatch,
      ));
    } else if (shouldClearSelectedMatch) {
      // Just clear the selected match
      emit(state.copyWith(
        hasNetworkCapability: event.hasNetworkCapability,
        selectedMatch: null,
      ));
    } else {
      emit(state.copyWith(hasNetworkCapability: event.hasNetworkCapability));
    }

    _logger.info('Network capability changed: ${event.hasNetworkCapability}');
  }

  // ========================================================================
  // UI STATE EVENT HANDLERS
  // ========================================================================

  void _onToggleServerProfileSelector(
    ToggleServerProfileSelectorEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final expanded = event.expanded ?? !state.serverProfileSelectorExpanded;
    emit(state.copyWith(serverProfileSelectorExpanded: expanded));
  }

  // ========================================================================
  // HELPER METHODS
  // ========================================================================

  /// Join a match by ID using the appropriate repository
  Future<bool> _joinMatchById(String matchId, {String? playerId}) async {
    // Try to find the match in our current state to determine source
    GameMatch? targetMatch;
    String? sourceName;

    for (final entry in state.matchesBySource.entries) {
      try {
        final match = entry.value.firstWhere((m) => m.id == matchId);
        targetMatch = match;
        sourceName = entry.key;
        break;
      } catch (e) {
        // Match not found in this source, continue to next
        continue;
      }
    }

    if (targetMatch == null || sourceName == null) {
      _logger.warn('Match $matchId not found in any source');
      return false;
    }

    final repository = _matchRepositories[sourceName];
    if (repository == null) {
      _logger.error('Repository not found for source: $sourceName');
      return false;
    }

    return await repository.joinMatch(matchId, playerId: playerId);
  }

  /// Leave a match by ID using the appropriate repository
  Future<bool> _leaveMatchById(String matchId, {String? playerId}) async {
    // Try all repositories since we might not know which one has the match
    // Create a snapshot to avoid concurrent modification during iteration
    final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
    for (final repository in repositoriesSnapshot.values) {
      try {
        final success = await repository.leaveMatch(matchId, playerId: playerId);
        if (success) {
          return true;
        }
      } catch (e) {
        _logger.warn('Failed to leave match $matchId from repository: $e');
      }
    }

    return false;
  }

  /// Handle match updates from a specific source
  void _handleMatchUpdatesFromSource(String sourceName, List<GameMatch> matches) {
    final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
    updatedMatchesBySource[sourceName] = matches;

    // Rebuild the complete matches list
    final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

    emit(state.copyWith(
      openMatches: allMatches,
      matchesBySource: updatedMatchesBySource,
    ));

    _logger.info('Received ${matches.length} match updates from $sourceName');
  }

  // ========================================================================
  // NETWORK SOURCE INTEGRATION
  // ========================================================================

  /// Check for and add available network sources
  Future<bool> _checkAndAddNetworkSources() async {
    try {
      // Check if we're in a server-connected scope (network available)
      if (GetIt.I.currentScopeName == 'serverConnected') {
        // Network repository will be added by onServerScopeAvailable() method
        // Just return true to indicate network capability is available
        _logger.info('Server-connected scope detected, network capability available');
        return true;
      } else {
        _logger.info('Not in server-connected scope, network sources unavailable');
      }

      return false;
    } catch (e) {
      _logger.warn('Failed to check network sources: $e');
      return false;
    }
  }

  /// Add a network match source when server connection is established
  /// @deprecated This method is deprecated as old use cases have been removed.
  /// Network repositories are now added automatically via DI.
  @Deprecated('Network repositories are now added automatically via DI')
  void addNetworkSource(MatchSelectionUseCase networkUseCase) {
    _logger.warn('addNetworkSource called with deprecated use case. Network repositories should be registered via DI.');
  }

  /// Re-initialize repositories when server scope becomes available
  /// This should be called when the server connection is established
  void onServerScopeAvailable() {
    _logger.info('Server scope became available, re-initializing repositories');
    _logger.info('Current GetIt scope: ${GetIt.I.currentScopeName}');

    // Re-initialize repositories to pick up network sources
    _initializeRepositories();

    // Update state with new sources
    final updatedSources = _matchRepositories.keys.toList();
    // Create snapshot to avoid concurrent modification during iteration
    final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
    final hasNetworkCapability = repositoriesSnapshot.values.any((repo) => repo.supportsRealTimeUpdates);

    emit(state.copyWith(
      availableMatchSources: updatedSources,
      hasNetworkCapability: hasNetworkCapability,
    ));

    _logger.info('Updated match sources after server scope activation: $updatedSources');

    // Subscribe to WebSocket updates when network becomes available
    if (hasNetworkCapability) {
      add(const SubscribeToMatchUpdatesEvent());
    }

    // Load matches from newly available sources
    add(const LoadMatchDataEvent());
  }

  /// Clean up when server scope is lost
  /// This should be called when the server connection is lost
  void onServerScopeLost() {
    _logger.info('Server scope lost, cleaning up network resources');
    _logger.info('Current GetIt scope: ${GetIt.I.currentScopeName}');

    // Remove network repository
    _matchRepositories.remove('network');

    // Update state
    final updatedSources = _matchRepositories.keys.toList();
    // Create snapshot to avoid concurrent modification during iteration
    final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
    final hasNetworkCapability = repositoriesSnapshot.values.any((repo) => repo.supportsRealTimeUpdates);

    // Check if we're currently creating a network match and clear it
    bool shouldClearMatchCreation = false;
    bool shouldClearSelectedMatch = false;

    if (!hasNetworkCapability) {
      // Check if currently creating a network match
      if (state.isCreatingMatch) {
        final hasNetworkPlayers = state.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (hasNetworkPlayers) {
          _logger.info('Clearing network match creation due to server scope loss');
          shouldClearMatchCreation = true;
        }
      }

      // Check if we have a selected network match for joining
      if (state.selectedMatch != null) {
        final selectedMatchHasNetworkPlayers = state.selectedMatch!.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (selectedMatchHasNetworkPlayers) {
          _logger.info('Clearing selected network match due to server scope loss');
          shouldClearSelectedMatch = true;
        }
      }
    }

    if (shouldClearMatchCreation) {
      // Clear match creation and return to selection mode
      emit(state.enterSelectionMode().copyWith(
        availableMatchSources: updatedSources,
        hasNetworkCapability: hasNetworkCapability,
        selectedConfigId: null,
        gameName: null,
        matchId: null,
        playerSlots: [],
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
        selectedMatch: shouldClearSelectedMatch ? null : state.selectedMatch,
      ));
    } else if (shouldClearSelectedMatch) {
      // Just clear the selected match
      emit(state.copyWith(
        availableMatchSources: updatedSources,
        hasNetworkCapability: hasNetworkCapability,
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
        selectedMatch: null,
      ));
    } else {
      emit(state.copyWith(
        availableMatchSources: updatedSources,
        hasNetworkCapability: hasNetworkCapability,
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
      ));
    }

    // Unsubscribe from WebSocket updates
    _unsubscribeFromWebSocketUpdates();

    _logger.info('Server scope cleanup completed, remaining sources: $updatedSources');
  }

  /// Remove a network match source when server connection is lost
  void removeNetworkSource(String sourceName) {
    _matchRepositories.remove(sourceName);

    // Update state
    final updatedSources = [...state.availableMatchSources];
    updatedSources.remove(sourceName);

    final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
    updatedMatchesBySource.remove(sourceName);

    // Create snapshot to avoid concurrent modification during iteration
    final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
    final hasNetworkCapability = repositoriesSnapshot.values.any((repo) => repo.supportsRealTimeUpdates);

    // Check if we're currently creating a network match and clear it if network capability is lost
    bool shouldClearMatchCreation = false;
    bool shouldClearSelectedMatch = false;

    if (!hasNetworkCapability) {
      // Check if currently creating a network match
      if (state.isCreatingMatch) {
        final hasNetworkPlayers = state.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (hasNetworkPlayers) {
          _logger.info('Clearing network match creation due to server disconnection');
          shouldClearMatchCreation = true;
        }
      }

      // Check if we have a selected network match for joining
      if (state.selectedMatch != null) {
        final selectedMatchHasNetworkPlayers = state.selectedMatch!.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (selectedMatchHasNetworkPlayers) {
          _logger.info('Clearing selected network match due to server disconnection');
          shouldClearSelectedMatch = true;
        }
      }
    }

    if (shouldClearMatchCreation) {
      // Clear match creation and return to selection mode
      emit(state.enterSelectionMode().copyWith(
        availableMatchSources: updatedSources,
        matchesBySource: updatedMatchesBySource,
        hasNetworkCapability: hasNetworkCapability,
        selectedConfigId: null,
        gameName: null,
        matchId: null,
        playerSlots: [],
        selectedMatch: shouldClearSelectedMatch ? null : state.selectedMatch,
      ));
    } else if (shouldClearSelectedMatch) {
      // Just clear the selected match
      emit(state.copyWith(
        availableMatchSources: updatedSources,
        matchesBySource: updatedMatchesBySource,
        hasNetworkCapability: hasNetworkCapability,
        selectedMatch: null,
      ));
    } else {
      emit(state.copyWith(
        availableMatchSources: updatedSources,
        matchesBySource: updatedMatchesBySource,
        hasNetworkCapability: hasNetworkCapability,
      ));
    }

    _logger.info('Removed network match source: $sourceName');

    // Unsubscribe from WebSocket updates if no network sources remain
    if (!hasNetworkCapability) {
      _unsubscribeFromWebSocketUpdates();
    }
  }

  /// Disconnect from server and clean up network resources
  void _disconnectFromServer() {
    try {
      _logger.info('Disconnecting from server');

      // Notify ServerEnvironmentManager to disconnect by setting profile to null
      try {
        GetIt.I<ServerEnvironmentManager>().add(SetSelectedServerProfileEvent(null));
      } catch (e) {
        _logger.warn('ServerEnvironmentManager not available for disconnect: $e');
      }

      _logger.info('Server disconnection completed');
    } catch (e) {
      _logger.error('Error during server disconnection: $e');
    }
  }

  // ========================================================================
  // WEBSOCKET INTEGRATION
  // ========================================================================

  /// Subscribe to WebSocket updates for real-time match synchronization
  void _subscribeToWebSocketUpdates() {
    try {
      // Only subscribe if we're not already subscribed
      if (_openMatchesSubscription != null) {
        _logger.info('Already subscribed to WebSocket updates');
        return;
      }

      // Get the server notifications use case
      final serverNotificationsUseCase = GetIt.I<ServerNotificationsUseCase>();

      // Subscribe to open matches updates
      serverNotificationsUseCase.subscribeToOpenMatches().then((_) {
        _logger.info('Subscribed to WebSocket open matches updates');

        // Listen for open matches updates from the WebSocket
        _openMatchesSubscription = serverNotificationsUseCase.openMatchesUpdates.listen(
          (matches) {
            _logger.info('🔄 WEBSOCKET UPDATE: Received ${matches.length} matches');
            // Log match IDs and player info for debugging
            for (final match in matches) {
              final playerCount = match.playerSlots.where((slot) => slot.playerId != null && slot.playerId!.isNotEmpty).length;
              _logger.info('  📋 Match ${match.id}: ${playerCount}/${match.playerSlots.length} players');
            }
            _handleWebSocketMatchUpdate(matches);
          },
          onError: (error) {
            _logger.error('Error in WebSocket open matches stream: $error');
          },
          onDone: () {
            _logger.info('WebSocket open matches stream closed');
            _openMatchesSubscription = null;
          },
        );
      }).catchError((error) {
        _logger.error('Failed to subscribe to WebSocket updates: $error');
      });

    } catch (e) {
      _logger.error('Failed to set up WebSocket subscription: $e');
    }
  }

  /// Unsubscribe from WebSocket updates
  void _unsubscribeFromWebSocketUpdates() {
    try {
      _openMatchesSubscription?.cancel();
      _openMatchesSubscription = null;

      // Unsubscribe from server notifications
      final serverNotificationsUseCase = GetIt.I<ServerNotificationsUseCase>();
      serverNotificationsUseCase.unsubscribeFromOpenMatches();

      _logger.info('Unsubscribed from WebSocket updates');
    } catch (e) {
      _logger.error('Failed to unsubscribe from WebSocket updates: $e');
    }
  }

  /// Handle WebSocket match updates
  void _handleWebSocketMatchUpdate(List<GameMatch> matches) {
    try {
      // Update the network source matches
      // Create a snapshot to avoid concurrent modification during iteration
      final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
      final networkSourceName = repositoriesSnapshot.entries
          .where((entry) => entry.value.supportsRealTimeUpdates)
          .map((entry) => entry.key)
          .firstOrNull;

      if (networkSourceName != null) {
        final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
        updatedMatchesBySource[networkSourceName] = matches;

        // Rebuild the complete matches list
        final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

        // Check if selectedMatch needs to be updated
        GameMatch? updatedSelectedMatch = state.selectedMatch;
        if (state.selectedMatch != null) {
          // Find the updated version of the selected match
          final matchingMatch = matches.firstWhere(
            (match) => match.id == state.selectedMatch!.id,
            orElse: () => state.selectedMatch!,
          );
          if (matchingMatch.id == state.selectedMatch!.id) {
            updatedSelectedMatch = matchingMatch;
            _logger.info('🔄 WEBSOCKET: Updated selectedMatch ${matchingMatch.id}');
          }
        }

        emit(state.copyWith(
          openMatches: allMatches,
          matchesBySource: updatedMatchesBySource,
          selectedMatch: updatedSelectedMatch,
        ));

        _logger.info('🔄 WEBSOCKET APPLIED: Updated ${matches.length} matches from $networkSourceName');
        _logger.info('📊 TOTAL MATCHES: ${allMatches.length} matches now available');
      } else {
        _logger.warn('No network source found for WebSocket update');
      }
    } catch (e) {
      _logger.error('Failed to handle WebSocket match update: $e');
    }
  }

  // ========================================================================
  // NETWORK MATCH CREATION HELPERS
  // ========================================================================

  /// Helper method to create a server match
  Future<({bool created, String? matchId, GameMatch? match})> _createServerMatch(List<PlayerSlot> playerSlots) async {
    try {
      final gameConfig = state.selectedConfig;
      if (gameConfig == null) {
        throw Exception('No game configuration selected');
      }

      final user = GetIt.I<UserManager>().state.user;
      if (user == null) {
        throw Exception('No user logged in');
      }

      // Find a network repository to create the match
      final networkRepository = _matchRepositories.values
          .firstWhere((repo) => repo.supportsRealTimeUpdates,
              orElse: () => throw Exception('No network repository available'));

      // Create the match using the network repository
      final createdMatch = await networkRepository.createMatch(
        GameMatch(
          id: GenerateIdIfNeededConverter().fromJson(null),
          gameTypeId: gameConfig.id,
          playerSlots: playerSlots,
          status: MatchStatus.open,
          creatorId: user.id,
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
          gameName: state.gameName,
        ),
        gameConfig,
      );

      if (createdMatch != null) {
        _logger.info('Successfully created server match: ${createdMatch.id}');
        return (created: true, matchId: createdMatch.id, match: createdMatch);
      } else {
        throw Exception('Server returned null match');
      }
    } catch (e) {
      _logger.error('Failed to create server match: $e');
      return (created: false, matchId: null, match: null);
    }
  }

  /// Helper method to update player slots on the server
  Future<void> _updateServerMatchPlayerSlots(List<PlayerSlot> playerSlots) async {
    if (state.matchId == null) {
      throw Exception('No match ID available for server update');
    }

    final user = GetIt.I<UserManager>().state.user;
    if (user == null) {
      throw Exception('No user logged in');
    }

    try {
      // Try to get the server repository directly
      final serverRepository = GetIt.I<ServerRepository>();

      // Update the match with the new player slots configuration
      final success = await serverRepository.updateMatchPlayerSlots(
        state.matchId!,
        playerSlots,
        user.id,
      );

      if (!success) {
        throw Exception('Server rejected player slots update');
      }

      _logger.info('Successfully updated match ${state.matchId} on server with new player slots');
    } catch (e) {
      _logger.error('Failed to update player slots on server: $e');
      rethrow;
    }
  }

  /// Check if a source is a local source
  bool _isLocalSource(String sourceName) {
    return sourceName.toLowerCase().contains('local') ||
           sourceName.toLowerCase().contains('file');
  }

  /// Check if a match is from a network source (server match)
  bool _isNetworkMatch(GameMatch match, MatchManagementState currentState) {
    // Check if the match exists in any network source
    for (final entry in currentState.matchesBySource.entries) {
      final sourceName = entry.key;
      final matches = entry.value;

      // Network sources typically have names like "Network", "Server", etc.
      if (sourceName.toLowerCase().contains('network') ||
          sourceName.toLowerCase().contains('server')) {
        if (matches.any((m) => m.id == match.id)) {
          _logger.info('Match ${match.id} identified as network match from source: $sourceName');
          return true;
        }
      }
    }
    return false;
  }

  // ========================================================================
  // CLEANUP
  // ========================================================================

  @override
  Future<void> close() async {
    // Cancel all stream subscriptions
    for (final subscription in _sourceSubscriptions.values) {
      await subscription.cancel();
    }
    _sourceSubscriptions.clear();

    // Cancel WebSocket subscription
    _unsubscribeFromWebSocketUpdates();

    _logger.info('MatchManagementBloc closed');
    return super.close();
  }
}


